"""
福彩3D特征计算测试
验证所有特征计算的正确性，确保与用户需求一致
"""

import unittest
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.data.feature_calculator import LotteryFeatureCalculator, calculate_features
from src.database.models import LotteryData


class TestFeatureCalculator(unittest.TestCase):
    """特征计算器测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.calculator = LotteryFeatureCalculator()
    
    def test_basic_features(self):
        """测试基础特征计算"""
        # 测试用例：123
        result = self.calculator.calculate_all_features(1, 2, 3)
        
        # 验证和值相关特征
        self.assertEqual(result.basic_features['sum_value'], 6)
        self.assertEqual(result.basic_features['sum_tail'], 6)  # 和尾
        
        # 验证跨度
        self.assertEqual(result.basic_features['span'], 2)  # max(1,2,3) - min(1,2,3) = 3-1 = 2
        
        # 验证奇偶特征
        self.assertEqual(result.basic_features['odd_count'], 2)  # 1,3是奇数
        self.assertEqual(result.basic_features['even_count'], 1)  # 2是偶数
        self.assertEqual(result.basic_features['odd_even_pattern'], '奇偶奇')
        
        # 验证大小特征
        self.assertEqual(result.basic_features['big_count'], 0)  # 都小于5
        self.assertEqual(result.basic_features['small_count'], 3)  # 都小于5
        self.assertEqual(result.basic_features['big_small_pattern'], '小小小')
        
        # 验证质合特征
        self.assertEqual(result.basic_features['prime_count'], 2)  # 2,3是质数
        self.assertEqual(result.basic_features['prime_composite_pattern'], '特质质')  # 1是特殊数
    
    def test_pattern_features(self):
        """测试模式特征计算"""
        # 测试连号：123
        result = self.calculator.calculate_all_features(1, 2, 3)
        self.assertTrue(result.pattern_features['has_consecutive'])
        self.assertEqual(result.pattern_features['consecutive_count'], 2)  # 1-2, 2-3
        
        # 测试豹子：111
        result = self.calculator.calculate_all_features(1, 1, 1)
        self.assertTrue(result.pattern_features['is_leopard'])
        self.assertEqual(result.pattern_features['number_type'], '豹子')
        self.assertTrue(result.pattern_features['is_parallel'])
        
        # 测试对子：112
        result = self.calculator.calculate_all_features(1, 1, 2)
        self.assertTrue(result.pattern_features['is_pair'])
        self.assertEqual(result.pattern_features['number_type'], '对子')
        
        # 测试形态特征
        # 凸起形：131
        result = self.calculator.calculate_all_features(1, 3, 1)
        self.assertTrue(result.pattern_features['is_convex'])
        
        # 凹下形：313
        result = self.calculator.calculate_all_features(3, 1, 3)
        self.assertTrue(result.pattern_features['is_concave'])
        
        # 上升形：123
        result = self.calculator.calculate_all_features(1, 2, 3)
        self.assertTrue(result.pattern_features['is_ascending'])
        
        # 下降形：321
        result = self.calculator.calculate_all_features(3, 2, 1)
        self.assertTrue(result.pattern_features['is_descending'])
    
    def test_trend_features(self):
        """测试走势特征计算"""
        # 测试走势：当前123，上期012
        result = self.calculator.calculate_all_features(1, 2, 3, 0, 1, 2)
        
        # 验证各位走势
        self.assertEqual(result.trend_features['hundreds_trend'], '上升')  # 1 > 0
        self.assertEqual(result.trend_features['tens_trend'], '上升')      # 2 > 1
        self.assertEqual(result.trend_features['units_trend'], '上升')     # 3 > 2
        self.assertEqual(result.trend_features['overall_trend'], '上升')
        
        # 测试下降走势：当前012，上期123
        result = self.calculator.calculate_all_features(0, 1, 2, 1, 2, 3)
        self.assertEqual(result.trend_features['hundreds_trend'], '下降')
        self.assertEqual(result.trend_features['tens_trend'], '下降')
        self.assertEqual(result.trend_features['units_trend'], '下降')
        self.assertEqual(result.trend_features['overall_trend'], '下降')
        
        # 测试平稳走势：当前123，上期123
        result = self.calculator.calculate_all_features(1, 2, 3, 1, 2, 3)
        self.assertEqual(result.trend_features['hundreds_trend'], '平稳')
        self.assertEqual(result.trend_features['tens_trend'], '平稳')
        self.assertEqual(result.trend_features['units_trend'], '平稳')
        self.assertEqual(result.trend_features['overall_trend'], '平稳')
    
    def test_big_medium_small_features(self):
        """测试大中小特征"""
        # 测试用例：147 (小中大)
        result = self.calculator.calculate_all_features(1, 4, 7)
        
        bms_count = result.basic_features['big_medium_small_count']
        self.assertEqual(bms_count['small_count'], 1)   # 1是小数(0-3)
        self.assertEqual(bms_count['medium_count'], 1)  # 4是中数(4-6)
        self.assertEqual(bms_count['big_count'], 1)     # 7是大数(7-9)
        
        self.assertEqual(result.basic_features['big_medium_small_pattern'], '小中大')
    
    def test_route_012_features(self):
        """测试012路特征"""
        # 测试用例：147
        # 1%3=1, 4%3=1, 7%3=1 -> 都是1路
        result = self.calculator.calculate_all_features(1, 4, 7)
        
        route_count = result.basic_features['route_012_count']
        self.assertEqual(route_count['route_0_count'], 0)
        self.assertEqual(route_count['route_1_count'], 3)
        self.assertEqual(route_count['route_2_count'], 0)
        
        self.assertEqual(result.basic_features['route_012_pattern'], '111')
        
        # 测试用例：036
        # 0%3=0, 3%3=0, 6%3=0 -> 都是0路
        result = self.calculator.calculate_all_features(0, 3, 6)
        route_count = result.basic_features['route_012_count']
        self.assertEqual(route_count['route_0_count'], 3)
        self.assertEqual(route_count['route_1_count'], 0)
        self.assertEqual(route_count['route_2_count'], 0)
        
        self.assertEqual(result.basic_features['route_012_pattern'], '000')
    
    def test_convenience_function(self):
        """测试便捷函数"""
        # 测试不带走势的计算
        features = calculate_features(1, 2, 3)
        self.assertIn('sum_value', features)
        self.assertIn('sum_tail', features)
        self.assertIn('has_consecutive', features)
        
        # 测试带走势的计算
        features = calculate_features(1, 2, 3, 0, 1, 2)
        self.assertIn('hundreds_trend', features)
        self.assertEqual(features['hundreds_trend'], '上升')
    
    def test_lottery_data_integration(self):
        """测试与LotteryData模型的集成"""
        # 创建测试数据
        current_data = LotteryData(
            issue="2025001", draw_date="2025-01-01",
            hundreds=1, tens=2, units=3
        )
        
        previous_data = LotteryData(
            issue="2024365", draw_date="2024-12-31",
            hundreds=0, tens=1, units=2
        )
        
        # 获取特征
        features = current_data.get_features(previous_data)
        
        # 验证基础字段存在
        self.assertEqual(features['issue'], "2025001")
        self.assertEqual(features['hundreds'], 1)
        self.assertEqual(features['tens'], 2)
        self.assertEqual(features['units'], 3)
        
        # 验证计算特征存在
        self.assertIn('sum_tail', features)
        self.assertIn('has_consecutive', features)
        self.assertIn('hundreds_trend', features)
        
        # 验证走势计算正确
        self.assertEqual(features['hundreds_trend'], '上升')
    
    def test_edge_cases(self):
        """测试边界情况"""
        # 测试最小值：000
        result = self.calculator.calculate_all_features(0, 0, 0)
        self.assertEqual(result.basic_features['sum_value'], 0)
        self.assertEqual(result.basic_features['sum_tail'], 0)
        self.assertEqual(result.basic_features['span'], 0)
        self.assertTrue(result.pattern_features['is_leopard'])
        
        # 测试最大值：999
        result = self.calculator.calculate_all_features(9, 9, 9)
        self.assertEqual(result.basic_features['sum_value'], 27)
        self.assertEqual(result.basic_features['sum_tail'], 7)
        self.assertEqual(result.basic_features['span'], 0)
        self.assertTrue(result.pattern_features['is_leopard'])
        
        # 测试无上期数据的走势计算
        result = self.calculator.calculate_all_features(1, 2, 3)
        self.assertEqual(result.trend_features['hundreds_trend'], '无')
        self.assertEqual(result.trend_features['overall_trend'], '无')
    
    def test_feature_completeness(self):
        """测试特征完整性"""
        result = self.calculator.calculate_all_features(1, 2, 3, 0, 1, 2)
        all_features = result.to_dict()
        
        # 验证必要特征存在
        required_features = [
            'sum_value', 'sum_tail', 'span',
            'odd_count', 'even_count', 'odd_even_pattern',
            'big_count', 'small_count', 'big_small_pattern',
            'prime_count', 'prime_composite_pattern',
            'route_012_pattern', 'has_consecutive',
            'is_convex', 'is_concave', 'is_ascending', 'is_descending',
            'hundreds_trend', 'tens_trend', 'units_trend', 'overall_trend'
        ]
        
        for feature in required_features:
            self.assertIn(feature, all_features, f"缺少特征: {feature}")
        
        # 验证特征数量（应该有足够多的特征）
        self.assertGreater(len(all_features), 30, "特征数量不足")


class TestFeatureValidation(unittest.TestCase):
    """特征验证测试类"""
    
    def test_feature_value_ranges(self):
        """测试特征值范围的合理性"""
        calculator = LotteryFeatureCalculator()
        
        # 测试多个随机用例
        test_cases = [
            (0, 0, 0), (1, 2, 3), (9, 9, 9), (5, 5, 5),
            (1, 5, 9), (2, 4, 6), (0, 5, 9), (3, 6, 9)
        ]
        
        for hundreds, tens, units in test_cases:
            result = calculator.calculate_all_features(hundreds, tens, units)
            features = result.to_dict()
            
            # 验证和值范围
            self.assertGreaterEqual(features['sum_value'], 0)
            self.assertLessEqual(features['sum_value'], 27)
            
            # 验证和尾范围
            self.assertGreaterEqual(features['sum_tail'], 0)
            self.assertLessEqual(features['sum_tail'], 9)
            
            # 验证跨度范围
            self.assertGreaterEqual(features['span'], 0)
            self.assertLessEqual(features['span'], 9)
            
            # 验证计数特征
            self.assertGreaterEqual(features['odd_count'], 0)
            self.assertLessEqual(features['odd_count'], 3)
            self.assertGreaterEqual(features['even_count'], 0)
            self.assertLessEqual(features['even_count'], 3)
            self.assertEqual(features['odd_count'] + features['even_count'], 3)


if __name__ == '__main__':
    # 运行所有测试
    unittest.main(verbosity=2)
