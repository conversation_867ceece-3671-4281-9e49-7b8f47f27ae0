#!/usr/bin/env python3
"""
快速测试修正后的特征计算
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

try:
    from src.data.feature_calculator import calculate_features
    print("成功导入特征计算器")
except ImportError as e:
    print(f"导入失败: {e}")
    sys.exit(1)

def test_corrected_features():
    """测试修正后的特征计算"""
    print("=" * 60)
    print("修正后的特征计算测试")
    print("=" * 60)

    try:
        # 测试号码 123
        result = calculate_features(1, 2, 3)

        print("测试号码 123:")
        print(f"- 和值: {result['sum_value']}, 和尾: {result['sum_tail']}, 跨度: {result['span']}")
        print(f"- 奇偶模式: {result['odd_even_pattern']}, 大小模式: {result['big_small_pattern']}")
        print(f"- 质合模式: {result['prime_composite_pattern']}, 012路: {result['route_012_pattern']}")
        print(f"- 连号: {result['has_consecutive']}, 上升形: {result['is_ascending']}")

        print("\n详细验证:")
        print(f"质合特征验证:")
        print(f"  - 1: 质数 ✓ (1 2 3 5 7为质)")
        print(f"  - 2: 质数 ✓ (1 2 3 5 7为质)")
        print(f"  - 3: 质数 ✓ (1 2 3 5 7为质)")
        print(f"  - 质合模式: {result['prime_composite_pattern']} (应该是'质质质')")

        print(f"\n012路特征验证:")
        print(f"  - 1: 1路 ✓ (147为1路)")
        print(f"  - 2: 2路 ✓ (258为2路)")
        print(f"  - 3: 0路 ✓ (0369为0路)")
        print(f"  - 012路模式: {result['route_012_pattern']} (应该是'120')")

        # 验证结果
        if result['prime_composite_pattern'] == '质质质' and result['route_012_pattern'] == '120':
            print("\n✅ 修正成功！特征计算现在符合用户要求")
        else:
            print(f"\n❌ 仍有问题：质合={result['prime_composite_pattern']}, 012路={result['route_012_pattern']}")

    except Exception as e:
        print(f"计算出错: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试其他用例
    print("\n" + "=" * 60)
    print("其他测试用例")
    print("=" * 60)
    
    test_cases = [
        (0, 4, 8, "合合合", "000"),  # 全合数，全0路
        (1, 4, 7, "质合质", "111"),  # 质合质，全1路
        (2, 5, 8, "质质合", "222"),  # 质质合，全2路
        (0, 3, 6, "合质合", "000"),  # 合质合，全0路
    ]
    
    for h, t, u, expected_prime, expected_route in test_cases:
        result = calculate_features(h, t, u)
        print(f"号码 {h}{t}{u}:")
        print(f"  质合模式: {result['prime_composite_pattern']} (期望: {expected_prime})")
        print(f"  012路: {result['route_012_pattern']} (期望: {expected_route})")
        print(f"  验证: {'✓' if result['prime_composite_pattern'] == expected_prime and result['route_012_pattern'] == expected_route else '✗'}")
        print()

if __name__ == "__main__":
    test_corrected_features()
